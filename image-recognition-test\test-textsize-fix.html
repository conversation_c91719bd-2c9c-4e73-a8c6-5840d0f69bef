<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TextSize 属性修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-element {
            border: 1px dashed #1890ff;
            background: rgba(24, 144, 255, 0.1);
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2px;
        }
        .size-info {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TextSize 属性修复验证</h1>
        
        <div class="comparison">
            <div>
                <h3>修复前（错误）</h3>
                <p><strong>问题</strong>：忽略 textSize，根据高度动态计算</p>
                
                <!-- 模拟修复前的渲染效果 -->
                <div class="test-element" style="width: 200px; height: 65px; font-size: 39px;">
                    合格证
                    <div class="size-info">height: 3.25mm → 39px字体</div>
                </div>
                
                <div class="test-element" style="width: 460px; height: 52px; font-size: 31px;">
                    品牌：Q.G Townlet
                    <div class="size-info">height: 2.58mm → 31px字体</div>
                </div>
                
                <div class="test-element" style="width: 430px; height: 50px; font-size: 30px;">
                    品名：儿童打底裤
                    <div class="size-info">height: 2.5mm → 30px字体</div>
                </div>
                
                <div class="test-element" style="width: 532px; height: 68px; font-size: 41px;">
                    零售价：98.00元
                    <div class="size-info">height: 3.42mm → 41px字体</div>
                </div>
            </div>
            
            <div>
                <h3>修复后（正确）</h3>
                <p><strong>修复</strong>：尊重 textSize 属性，统一字体大小</p>
                
                <!-- 模拟修复后的渲染效果，假设 scale = 20 (800px / 40mm) -->
                <div class="test-element" style="width: 200px; height: 65px; font-size: 240px; transform: scale(0.1); transform-origin: left center;">
                    合格证
                    <div class="size-info">textSize: 12mm → 240px字体</div>
                </div>
                
                <div class="test-element" style="width: 460px; height: 52px; font-size: 240px; transform: scale(0.1); transform-origin: left center;">
                    品牌：Q.G Townlet
                    <div class="size-info">textSize: 12mm → 240px字体</div>
                </div>
                
                <div class="test-element" style="width: 430px; height: 50px; font-size: 240px; transform: scale(0.1); transform-origin: left center;">
                    品名：儿童打底裤
                    <div class="size-info">textSize: 12mm → 240px字体</div>
                </div>
                
                <div class="test-element" style="width: 532px; height: 68px; font-size: 240px; transform: scale(0.1); transform-origin: left center;">
                    零售价：98.00元
                    <div class="size-info">textSize: 12mm → 240px字体</div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f8f8; border-radius: 4px;">
            <h3>修复说明</h3>
            
            <h4>问题根源</h4>
            <div class="code-block">
// 修复前：忽略 textSize 属性
fontSize: `${Math.max(10, height * 0.6)}px`

// 结果：不同高度的元素字体大小不同，即使 textSize 都是 12
            </div>
            
            <h4>修复方案</h4>
            <div class="code-block">
// 修复后：优先使用 textSize 属性
const textSizeMm = parseFloat(element.textSize) || 12;
const fontSize = element.textSize ? textSizeMm * scale : calculatedFontSize;

// 结果：所有 textSize=12 的元素字体大小一致
            </div>
            
            <h4>修复效果</h4>
            <ul>
                <li>✅ <strong>尊重数据</strong>：使用 XPrinter 数据中的 textSize 属性</li>
                <li>✅ <strong>一致性</strong>：相同 textSize 的元素字体大小相同</li>
                <li>✅ <strong>准确性</strong>：字体大小与原始设计意图一致</li>
                <li>✅ <strong>兼容性</strong>：没有 textSize 时仍使用动态计算作为备选</li>
            </ul>
            
            <h4>计算公式</h4>
            <div class="code-block">
// 普通文本
fontSize = textSize ? (textSizeMm * scale) : (height * 0.6)

// 表格文本  
fontSize = cell.textSize ? (textSizeMm * scale) : (dynamicCalculation)

// 其中 scale = displayWidth / canvasWidthMm
            </div>
        </div>
    </div>
</body>
</html>
