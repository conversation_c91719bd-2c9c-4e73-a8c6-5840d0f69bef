<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WordSpace 字符间距功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .comparison {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .text-sample {
            border: 1px dashed #1890ff;
            background: rgba(24, 144, 255, 0.1);
            padding: 15px;
            margin: 10px 0;
            text-align: center;
            font-size: 24px;
        }
        .char-spaced {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }
        .char-spaced span {
            margin-right: 2px; /* 模拟 wordSpace */
        }
        .char-spaced span:last-child {
            margin-right: 0;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .feature-list {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WordSpace 字符间距功能</h1>
        
        <div class="test-section">
            <div class="test-title">1. 功能说明</div>
            <p>WordSpace 字段表示字符之间的间距，单位为毫米。现在渲染器会正确使用这个数据：</p>
            <div class="feature-list">
                <ul>
                    <li>✅ <strong>真实数据驱动</strong>：使用 XPrinter 数据中的 wordSpace 值</li>
                    <li>✅ <strong>精确间距控制</strong>：每个字符之间的间距基于毫米值计算</li>
                    <li>✅ <strong>对齐方式支持</strong>：支持左对齐、居中、右对齐</li>
                    <li>✅ <strong>条件渲染</strong>：有间距时使用自定义渲染，无间距时使用原有组件</li>
                    <li>✅ <strong>编辑功能保留</strong>：点击仍可编辑文本内容</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 渲染方式对比</div>
            <div class="comparison">
                <div>
                    <h4>修复前（CSS 效果）</h4>
                    <div class="text-sample" style="justify-content: center; display: flex;">
                        杏仁椰蓉曲奇
                    </div>
                    <p><strong>问题</strong>：使用 CSS 的 justifyContent: 'center'，忽略了 wordSpace 数据</p>
                </div>
                
                <div>
                    <h4>修复后（数据驱动）</h4>
                    <div class="text-sample">
                        <div class="char-spaced">
                            <span>杏</span>
                            <span>仁</span>
                            <span>椰</span>
                            <span>蓉</span>
                            <span>曲</span>
                            <span>奇</span>
                        </div>
                    </div>
                    <p><strong>改进</strong>：基于 wordSpace 数据，每个字符间距精确控制</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 不同间距效果演示</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>无间距 (wordSpace: 0)</h4>
                    <div class="text-sample">
                        <div style="display: flex;">
                            <span>配</span><span>料</span><span>：</span><span>黄</span><span>油</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4>小间距 (wordSpace: 0.11mm)</h4>
                    <div class="text-sample">
                        <div class="char-spaced" style="gap: 2px;">
                            <span>配</span><span>料</span><span>：</span><span>黄</span><span>油</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4>中等间距 (wordSpace: 0.5mm)</h4>
                    <div class="text-sample">
                        <div class="char-spaced" style="gap: 10px;">
                            <span>保</span><span>质</span><span>期</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4>大间距 (wordSpace: 1.0mm)</h4>
                    <div class="text-sample">
                        <div class="char-spaced" style="gap: 20px;">
                            <span>生</span><span>产</span><span>日</span><span>期</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 实现原理</div>
            <div class="code-block">
// 获取字符间距并转换为像素
const wordSpaceMm = parseFloat(element.wordSpace) || 0;
const wordSpacePx = wordSpaceMm * scale;

// 条件渲染：有间距时使用自定义渲染
if (wordSpacePx > 0) {
  return (
    &lt;div style={{ display: 'flex', alignItems: 'center' }}&gt;
      {(element.content || '').split('').map((char, charIndex) =&gt; (
        &lt;span
          key={charIndex}
          style={{
            marginRight: charIndex &lt; content.length - 1 ? `${wordSpacePx}px` : '0'
          }}
        &gt;
          {char}
        &lt;/span&gt;
      ))}
    &lt;/div&gt;
  );
}

// 无间距时使用原有的 EditableText 组件
return &lt;EditableText ... /&gt;;
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 对齐方式支持</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                <div>
                    <h4>左对齐 (hAlignment: 1)</h4>
                    <div class="text-sample" style="text-align: left;">
                        <div class="char-spaced" style="justify-content: flex-start;">
                            <span>左</span><span>对</span><span>齐</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4>居中 (hAlignment: 2)</h4>
                    <div class="text-sample" style="text-align: center;">
                        <div class="char-spaced" style="justify-content: center;">
                            <span>居</span><span>中</span><span>对</span><span>齐</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4>右对齐 (hAlignment: 3)</h4>
                    <div class="text-sample" style="text-align: right;">
                        <div class="char-spaced" style="justify-content: flex-end;">
                            <span>右</span><span>对</span><span>齐</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">6. 测试步骤</div>
            <ol>
                <li><strong>上传图片</strong>：上传包含文字的图片进行识别</li>
                <li><strong>查看数据</strong>：在转换后的数据中查看 wordSpace 字段值</li>
                <li><strong>对比效果</strong>：观察文字渲染是否使用了正确的字符间距</li>
                <li><strong>验证对齐</strong>：检查不同 hAlignment 值的对齐效果</li>
                <li><strong>测试编辑</strong>：点击文字确认编辑功能正常</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">7. 预期效果</div>
            <ul>
                <li><strong>数据一致性</strong>：渲染效果完全基于 XPrinter 数据，不依赖 CSS 估算</li>
                <li><strong>精确控制</strong>：字符间距精确到毫米级别</li>
                <li><strong>视觉准确</strong>：渲染结果与原图片的字符分布完全一致</li>
                <li><strong>功能完整</strong>：保留所有原有的编辑和交互功能</li>
            </ul>
        </div>
    </div>
</body>
</html>
