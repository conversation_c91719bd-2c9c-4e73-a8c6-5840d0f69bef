<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体大小对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .comparison {
            display: flex;
            gap: 30px;
            margin: 20px 0;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-element {
            border: 1px dashed #1890ff;
            background: rgba(24, 144, 255, 0.1);
            margin: 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .test-table {
            border: 1px dashed #eb2f06;
            background: rgba(235, 47, 6, 0.1);
            margin: 10px 0;
            display: grid;
            gap: 1px;
            background-color: #ccc;
        }
        .test-cell {
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border: 1px solid #e8e8e8;
            padding: 2px 4px;
        }
        .size-info {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>普通文本 vs 表格文本字体大小对比</h1>
        
        <div class="comparison">
            <div>
                <h3>普通文本（原有逻辑）</h3>
                <p><strong>计算方式</strong>：height × 0.6</p>
                
                <!-- 模拟不同高度的普通文本 -->
                <div class="test-element" style="width: 200px; height: 40px; font-size: 24px;">
                    品名
                    <div class="size-info">40px高 → 24px字体</div>
                </div>
                
                <div class="test-element" style="width: 200px; height: 60px; font-size: 36px;">
                    女鞋
                    <div class="size-info">60px高 → 36px字体</div>
                </div>
                
                <div class="test-element" style="width: 300px; height: 80px; font-size: 48px;">
                    QB/T1002-2015
                    <div class="size-info">80px高 → 48px字体</div>
                </div>
            </div>
            
            <div>
                <h3>表格文本（新逻辑）</h3>
                <p><strong>计算方式</strong>：(cellHeight / lineCount) × 0.6</p>
                
                <!-- 模拟表格单元格 -->
                <div class="test-table" style="grid-template-columns: 1fr 1fr; grid-template-rows: 40px 60px 80px;">
                    <div class="test-cell" style="font-size: 24px;">
                        品名
                        <div class="size-info">40px高 → 24px字体</div>
                    </div>
                    <div class="test-cell" style="font-size: 24px;">
                        女鞋
                        <div class="size-info">40px高 → 24px字体</div>
                    </div>
                    
                    <div class="test-cell" style="font-size: 36px;">
                        货号
                        <div class="size-info">60px高 → 36px字体</div>
                    </div>
                    <div class="test-cell" style="font-size: 36px;">
                        023-552
                        <div class="size-info">60px高 → 36px字体</div>
                    </div>
                    
                    <div class="test-cell" style="font-size: 48px;">
                        执行标准
                        <div class="size-info">80px高 → 48px字体</div>
                    </div>
                    <div class="test-cell" style="font-size: 24px; white-space: pre-line; line-height: 1.2;">QB/T1002-2015
等级：合格品
                        <div class="size-info">80px高÷2行 → 24px字体</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #f8f8f8; border-radius: 4px;">
            <h3>改进说明</h3>
            <ul>
                <li><strong>统一计算方式</strong>：表格文本现在使用与普通文本相同的动态计算方式</li>
                <li><strong>多行文本支持</strong>：对于包含换行的单元格，字体大小会根据行数调整</li>
                <li><strong>实时渲染</strong>：字体大小在渲染时根据实际像素尺寸计算，而不是预计算</li>
                <li><strong>一致性</strong>：相同高度的文本元素和表格单元格现在具有相同的字体大小</li>
            </ul>
            
            <h4>核心改进</h4>
            <pre style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
// 普通文本（原有）
fontSize = Math.max(10, height * 0.6)

// 表格文本（新逻辑）
const lineCount = hasLineBreaks ? displayContent.split('\n').length : 1;
const adjustedCellHeight = cellHeightPx / lineCount;
fontSize = Math.max(10, Math.min(24, adjustedCellHeight * 0.6));
            </pre>
        </div>
    </div>
</body>
</html>
