<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字符边框显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .demo-area {
            position: relative;
            background: #f9f9f9;
            border: 1px solid #e8e8e8;
            margin: 20px 0;
            padding: 20px;
            min-height: 200px;
        }
        .char-bound {
            position: absolute;
            border: 1px solid red;
            background-color: rgba(255, 0, 0, 0.1);
            font-size: 10px;
            color: red;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: none;
        }
        .text-sample {
            position: absolute;
            font-size: 32px;
            color: #333;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .feature-list {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>字符边框显示功能</h1>
        
        <div class="test-section">
            <div class="test-title">1. 功能说明</div>
            <p>新增的字符边框显示功能可以：</p>
            <div class="feature-list">
                <ul>
                    <li>✅ <strong>可视化字符边界</strong>：显示 TextIn API 识别的每个字符的精确位置</li>
                    <li>✅ <strong>红色边框标记</strong>：每个字符用红色边框和半透明背景标出</li>
                    <li>✅ <strong>字符内容显示</strong>：在边框内显示对应的字符</li>
                    <li>✅ <strong>详细信息提示</strong>：鼠标悬停显示字符和坐标信息</li>
                    <li>✅ <strong>开关控制</strong>：可以通过复选框开启/关闭显示</li>
                    <li>✅ <strong>层级管理</strong>：字符边框在最上层，不影响正常操作</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 模拟效果演示</div>
            <p>以下是字符边框的模拟效果（基于你提供的 TextIn 数据）：</p>
            
            <div class="demo-area">
                <!-- 模拟文本 "杏仁椰蓉曲奇" -->
                <div class="text-sample" style="left: 133px; top: 39px; font-size: 32px;">杏仁椰蓉曲奇</div>
                
                <!-- 模拟字符边框 -->
                <div class="char-bound" style="left: 133px; top: 38px; width: 36px; height: 35px;">杏</div>
                <div class="char-bound" style="left: 173px; top: 42px; width: 33px; height: 30px;">仁</div>
                <div class="char-bound" style="left: 209px; top: 39px; width: 35px; height: 35px;">椰</div>
                <div class="char-bound" style="left: 246px; top: 37px; width: 37px; height: 38px;">蓉</div>
                <div class="char-bound" style="left: 286px; top: 38px; width: 33px; height: 35px;">曲</div>
                <div class="char-bound" style="left: 322px; top: 37px; width: 38px; height: 36px;">奇</div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 实现原理</div>
            <div class="code-block">
// 从 TextIn 数据中提取字符位置
const renderCharBounds = () => {
  const charBounds = [];
  
  pages.forEach(page => {
    page.content.forEach(contentItem => {
      contentItem.char_pos.forEach((charPos, charIndex) => {
        // 计算字符边界框
        const minX = Math.min(charPos[0], charPos[2], charPos[4], charPos[6]);
        const maxX = Math.max(charPos[0], charPos[2], charPos[4], charPos[6]);
        const minY = Math.min(charPos[1], charPos[3], charPos[5], charPos[7]);
        const maxY = Math.max(charPos[1], charPos[3], charPos[5], charPos[7]);
        
        // 转换为渲染坐标
        const x = minX * scale;
        const y = minY * scale;
        const width = (maxX - minX) * scale;
        const height = (maxY - minY) * scale;
        
        // 创建字符边框元素
        charBounds.push(
          &lt;div style={{
            position: 'absolute',
            left: `${x}px`,
            top: `${y}px`,
            width: `${width}px`,
            height: `${height}px`,
            border: '1px solid red',
            backgroundColor: 'rgba(255, 0, 0, 0.1)',
            pointerEvents: 'none',
            zIndex: 1000
          }}&gt;
            {contentItem.text[charIndex]}
          &lt;/div&gt;
        );
      });
    });
  });
  
  return charBounds;
};
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 数据结构分析</div>
            <p>基于你提供的 TextIn 数据，字符位置信息如下：</p>
            <div class="code-block">
// "杏仁椰蓉曲奇" 的字符位置数据
char_pos: [
  [133,38,169,39,169,73,133,73],  // "杏": 宽36px 高35px
  [173,42,206,43,206,72,173,72],  // "仁": 宽33px 高30px  
  [209,39,244,40,244,74,209,74],  // "椰": 宽35px 高35px
  [246,37,283,37,283,75,246,75],  // "蓉": 宽37px 高38px
  [286,38,319,38,319,73,286,73],  // "曲": 宽33px 高35px
  [322,37,360,38,360,73,322,73]   // "奇": 宽38px 高36px
]

// 每个字符的坐标格式：[x1,y1,x2,y2,x3,y3,x4,y4]
// 边界框计算：
// - minX = min(x1,x2,x3,x4)
// - maxX = max(x1,x2,x3,x4)  
// - minY = min(y1,y2,y3,y4)
// - maxY = max(y1,y2,y3,y4)
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 使用方法</div>
            <ol>
                <li><strong>上传图片</strong>：上传包含文字的图片进行识别</li>
                <li><strong>开启显示</strong>：勾选"显示字符边框"复选框</li>
                <li><strong>查看边框</strong>：红色边框会覆盖在每个识别的字符上</li>
                <li><strong>查看详情</strong>：鼠标悬停在边框上查看字符和坐标信息</li>
                <li><strong>调试字体</strong>：对比字符边框和渲染文字的大小是否匹配</li>
            </ol>
        </div>

        <div class="test-section">
            <div class="test-title">6. 调试价值</div>
            <p>字符边框显示功能的主要价值：</p>
            <ul>
                <li><strong>验证识别准确性</strong>：检查 TextIn 是否正确识别了字符边界</li>
                <li><strong>调试字体大小</strong>：对比计算的字体大小与实际字符大小</li>
                <li><strong>检查位置偏移</strong>：确认文字渲染位置是否与原图一致</li>
                <li><strong>分析字符间距</strong>：观察字符之间的间距和排列</li>
                <li><strong>优化算法</strong>：为字体大小计算算法提供可视化反馈</li>
            </ul>
        </div>
    </div>
</body>
</html>
