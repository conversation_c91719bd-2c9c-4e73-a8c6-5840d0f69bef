<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于字符位置的字体大小计算测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .comparison {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .text-sample {
            border: 1px dashed #1890ff;
            background: rgba(24, 144, 255, 0.1);
            margin: 10px 0;
            padding: 10px;
            text-align: center;
        }
        .size-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>基于字符位置的真实字体大小计算</h1>
        
        <div class="test-section">
            <div class="test-title">1. 字符位置数据分析</div>
            <p>TextIn API 返回的 <code>char_pos</code> 数据包含每个字符的精确位置坐标：</p>
            <div class="code-block">
// 示例：文本 "解冻标签" 的字符位置
char_pos: [
  [203, 38, 244, 38, 244, 76, 203, 76],  // "解" 字符位置
  [287, 36, 328, 37, 328, 75, 287, 75],  // "冻" 字符位置  
  [370, 37, 413, 37, 413, 77, 370, 77],  // "标" 字符位置
  [454, 37, 497, 37, 497, 77, 454, 76]   // "签" 字符位置
]

// 每个字符的坐标格式：[x1,y1,x2,y2,x3,y3,x4,y4]
// 字符高度计算：max(y5,y7) - min(y1,y3)
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 字体大小计算逻辑</div>
            <div class="comparison">
                <div>
                    <h4>修复前（固定值）</h4>
                    <div class="text-sample" style="font-size: 12px;">
                        解冻标签
                        <div class="size-info">textSize: 12px（固定）</div>
                    </div>
                    <div class="text-sample" style="font-size: 12px;">
                        品名：
                        <div class="size-info">textSize: 12px（固定）</div>
                    </div>
                    <div class="text-sample" style="font-size: 12px;">
                        解冻时间：
                        <div class="size-info">textSize: 12px（固定）</div>
                    </div>
                </div>
                
                <div>
                    <h4>修复后（基于字符位置）</h4>
                    <div class="text-sample" style="font-size: 32px;">
                        解冻标签
                        <div class="size-info">字符高度: 38px → textSize: 32px</div>
                    </div>
                    <div class="text-sample" style="font-size: 30px;">
                        品名：
                        <div class="size-info">字符高度: 35px → textSize: 30px</div>
                    </div>
                    <div class="text-sample" style="font-size: 31px;">
                        解冻时间：
                        <div class="size-info">字符高度: 36px → textSize: 31px</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 计算公式</div>
            <div class="code-block">
/**
 * 根据字符位置信息计算真实字体大小
 */
calculateFontSizeFromCharPos(charPos) {
  // 1. 计算每个字符的高度
  const charHeights = charPos.map(pos => {
    const topY = Math.min(pos[1], pos[3]);
    const bottomY = Math.max(pos[5], pos[7]);
    return bottomY - topY;
  });
  
  // 2. 使用平均高度
  const avgHeight = charHeights.reduce((sum, h) => sum + h, 0) / charHeights.length;
  
  // 3. 字体大小 = 字符高度 × 0.85（经验系数）
  const fontSize = Math.round(avgHeight * 0.85);
  
  // 4. 限制在合理范围内 (8-72px)
  return Math.max(8, Math.min(72, fontSize));
}
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 实际应用场景</div>
            <div class="comparison">
                <div>
                    <h4>普通文本</h4>
                    <ul>
                        <li>从 <code>pageContent</code> 中查找匹配的文本行</li>
                        <li>提取 <code>char_pos</code> 数据</li>
                        <li>计算真实字体大小</li>
                        <li>设置 <code>textSize</code> 属性</li>
                    </ul>
                </div>
                
                <div>
                    <h4>表格文本</h4>
                    <ul>
                        <li>从单元格内容匹配 <code>pageContent</code></li>
                        <li>处理多行文本（<code>&lt;br&gt;</code> 分隔）</li>
                        <li>分别计算每行的字体大小</li>
                        <li>使用平均值或最小值</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 优势与效果</div>
            <ul>
                <li>✅ <strong>真实准确</strong>：基于实际字符尺寸，而非估算</li>
                <li>✅ <strong>自动适应</strong>：不同大小的文字自动计算合适的字体大小</li>
                <li>✅ <strong>视觉一致</strong>：渲染结果与原图片高度一致</li>
                <li>✅ <strong>智能备选</strong>：没有字符位置信息时使用默认值</li>
                <li>✅ <strong>范围限制</strong>：避免过大或过小的字体</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">6. 测试步骤</div>
            <ol>
                <li>上传包含不同字体大小文本的图片</li>
                <li>查看控制台中的字体大小计算日志</li>
                <li>对比渲染结果与原图片的视觉效果</li>
                <li>验证表格单元格的字体大小是否合理</li>
            </ol>
        </div>
    </div>
</body>
</html>
