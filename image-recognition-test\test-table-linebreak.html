<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格换行测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .test-table td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }
        .multiline-cell {
            white-space: pre-line;
            line-height: 1.2;
        }
        .single-line-cell {
            white-space: nowrap;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表格换行功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 原始TextIn数据中的 &lt;br&gt; 标签</div>
            <div class="code-block">
                原始数据: "QB/T1002-2015&lt;br&gt;等级：合格品"
            </div>
            <table class="test-table">
                <tr>
                    <td>执行标准</td>
                    <td class="single-line-cell">QB/T1002-2015<br>等级：合格品</td>
                </tr>
            </table>
            <p><strong>期望结果</strong>: 应该显示为两行文本</p>
        </div>

        <div class="test-section">
            <div class="test-title">2. 使用 white-space: pre-line 的正确显示</div>
            <table class="test-table">
                <tr>
                    <td>执行标准</td>
                    <td class="multiline-cell">QB/T1002-2015
等级：合格品</td>
                </tr>
            </table>
            <p><strong>说明</strong>: 这是修复后应该达到的效果</p>
        </div>

        <div class="test-section">
            <div class="test-title">3. 其他多行文本示例</div>
            <table class="test-table">
                <tr>
                    <td>品名</td>
                    <td class="multiline-cell">女鞋
春季新款</td>
                </tr>
                <tr>
                    <td>规格</td>
                    <td class="multiline-cell">尺码: 36
颜色: 米灰桔
材质: 牛皮</td>
                </tr>
            </table>
        </div>

        <div class="test-section">
            <div class="test-title">4. 修复说明</div>
            <div class="code-block">
修复内容:
1. 在 LabelRenderer.jsx 中检测单元格内容是否包含 &lt;br&gt; 标签
2. 将 &lt;br&gt; 标签转换为换行符 \n
3. 对于包含换行符的单元格，使用 white-space: pre-line 样式
4. 对于单行文本，继续使用 EditableText 组件

关键代码:
- const hasLineBreaks = displayContent.includes('&lt;br&gt;');
- displayContent = displayContent.replace(/&lt;br\s*\/?&gt;/gi, '\n');
- whiteSpace: 'pre-line'
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 测试步骤</div>
            <ol>
                <li>上传包含多行表格单元格的图片</li>
                <li>确认TextIn API返回的数据中包含 &lt;br&gt; 标签</li>
                <li>检查渲染后的表格是否正确显示换行</li>
                <li>测试编辑功能是否正常工作</li>
            </ol>
        </div>
    </div>
</body>
</html>
