<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体大小系数调整测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .coefficient-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-sample {
            border: 1px dashed #1890ff;
            background: rgba(24, 144, 255, 0.1);
            padding: 15px;
            text-align: center;
            border-radius: 4px;
        }
        .size-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .char-analysis {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .char-box {
            display: inline-block;
            border: 1px solid #999;
            margin: 2px;
            padding: 2px 4px;
            background: white;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>字体大小系数调整测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 问题分析</div>
            <p><strong>现象</strong>：计算出的字体大小比实际显示效果偏小</p>
            <p><strong>原因</strong>：CSS 字体大小与字符实际高度的关系因字体类型而异</p>
            <ul>
                <li><strong>中文字符</strong>：通常字体大小 ≈ 字符高度 × 1.0-1.2</li>
                <li><strong>英文字符</strong>：通常字体大小 ≈ 字符高度 × 0.9-1.1</li>
                <li><strong>数字符号</strong>：通常字体大小 ≈ 字符高度 × 0.8-1.0</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">2. 不同系数效果对比</div>
            <p>假设字符高度为 38px，测试不同系数的效果：</p>
            
            <div class="coefficient-test">
                <div class="test-sample">
                    <div style="font-size: 32px;">解冻标签</div>
                    <div class="size-info">系数 0.85<br>38px × 0.85 = 32px</div>
                </div>
                
                <div class="test-sample">
                    <div style="font-size: 38px;">解冻标签</div>
                    <div class="size-info">系数 1.0<br>38px × 1.0 = 38px</div>
                </div>
                
                <div class="test-sample">
                    <div style="font-size: 42px;">解冻标签</div>
                    <div class="size-info">系数 1.1<br>38px × 1.1 = 42px</div>
                </div>
                
                <div class="test-sample">
                    <div style="font-size: 46px;">解冻标签</div>
                    <div class="size-info">系数 1.2<br>38px × 1.2 = 46px</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 智能系数选择</div>
            <p>新的计算逻辑根据字符宽高比自动选择系数：</p>
            
            <div class="char-analysis">
                <h4>中文字符分析</h4>
                <div class="char-box">解: 宽41px 高38px 比例1.08</div>
                <div class="char-box">冻: 宽41px 高39px 比例1.05</div>
                <div class="char-box">标: 宽43px 高40px 比例1.08</div>
                <div class="char-box">签: 宽43px 高39px 比例1.10</div>
                <p><strong>平均宽高比</strong>: 1.08 > 0.8 → 使用系数 1.1</p>
            </div>
            
            <div class="char-analysis">
                <h4>英文字符分析</h4>
                <div class="char-box">Q: 宽18px 高32px 比例0.56</div>
                <div class="char-box">B: 宽15px 高32px 比例0.47</div>
                <div class="char-box">T: 宽16px 高32px 比例0.50</div>
                <div class="char-box">1: 宽8px 高32px 比例0.25</div>
                <p><strong>平均宽高比</strong>: 0.45 < 0.8 → 使用系数 1.0</p>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 新的计算逻辑</div>
            <pre style="background: #f8f8f8; padding: 15px; border-radius: 4px; overflow-x: auto;">
// 计算字符宽高比
const aspectRatio = avgWidth / avgHeight;

// 根据宽高比选择系数
if (aspectRatio > 0.8) {
  // 接近正方形，可能是中文字符
  fontSize = Math.round(avgHeight * 1.1);
} else {
  // 较窄，可能是英文字符
  fontSize = Math.round(avgHeight * 1.0);
}
            </pre>
        </div>

        <div class="test-section">
            <div class="test-title">5. 进一步调整建议</div>
            <p>如果字体大小仍然偏小，可以考虑：</p>
            <ul>
                <li><strong>增加系数</strong>：中文 1.1 → 1.2，英文 1.0 → 1.1</li>
                <li><strong>考虑字体类型</strong>：不同字体的度量差异</li>
                <li><strong>添加最小增量</strong>：fontSize = Math.max(calculatedSize, avgHeight + 2)</li>
                <li><strong>用户反馈调整</strong>：根据实际效果微调系数</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">6. 测试步骤</div>
            <ol>
                <li>上传包含不同类型文字的图片</li>
                <li>查看控制台中的字符尺寸分析日志</li>
                <li>对比渲染结果与原图片</li>
                <li>如果仍然偏小，可以进一步调整系数</li>
            </ol>
        </div>
    </div>

    <script>
        // 模拟字符尺寸分析
        function analyzeCharacter(char, width, height) {
            const aspectRatio = width / height;
            const coefficient = aspectRatio > 0.8 ? 1.1 : 1.0;
            const fontSize = Math.round(height * coefficient);
            
            console.log(`字符 "${char}" 分析:`, {
                width, height, aspectRatio: aspectRatio.toFixed(2),
                coefficient, fontSize
            });
            
            return fontSize;
        }

        // 示例分析
        console.log('=== 字符尺寸分析示例 ===');
        analyzeCharacter('解', 41, 38);
        analyzeCharacter('冻', 41, 39);
        analyzeCharacter('Q', 18, 32);
        analyzeCharacter('B', 15, 32);
    </script>
</body>
</html>
