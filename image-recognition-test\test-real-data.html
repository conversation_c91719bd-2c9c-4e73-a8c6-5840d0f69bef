<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .code-block {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #40a9ff;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>真实TextIn数据测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 原始TextIn API数据</div>
            <div class="code-block" id="originalData">
{
  "detail": [{
    "type": "table",
    "cells": [{
      "col": 1,
      "col_span": 2,
      "row": 3,
      "row_span": 1,
      "text": "QB/T1002-2015<br>等级：合格品"
    }]
  }]
}
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">2. 测试数据转换</div>
            <button class="test-button" onclick="testDataConversion()">测试数据转换</button>
            <button class="test-button" onclick="testRenderLogic()">测试渲染逻辑</button>
            <div id="result"></div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 调试信息</div>
            <p>请打开浏览器开发者工具的控制台查看详细的调试信息</p>
            <p>特别关注以下信息：</p>
            <ul>
                <li>dataConverter.js 中的单元格内容获取日志</li>
                <li>LabelRenderer.jsx 中的单元格渲染日志</li>
                <li>是否检测到 &lt;br&gt; 标签</li>
                <li>是否正确转换为换行符</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟TextIn数据
        const mockTextInData = {
            "result": {
                "detail": [{
                    "sub_type": "bordered",
                    "type": "table",
                    "cells": [{
                        "col": 1,
                        "col_span": 2,
                        "page_id": 1,
                        "position": [190, 347, 686, 347, 686, 454, 190, 454],
                        "row": 3,
                        "row_span": 1,
                        "text": "QB/T1002-2015<br>等级：合格品",
                        "type": "cell"
                    }]
                }]
            }
        };

        function testDataConversion() {
            console.log('=== 开始测试数据转换 ===');
            console.log('原始TextIn数据:', mockTextInData);
            
            const cell = mockTextInData.result.detail[0].cells[0];
            console.log('单元格原始文本:', cell.text);
            console.log('包含<br>标签:', cell.text.includes('<br>'));
            
            // 模拟dataConverter的处理逻辑
            let cellContent = cell.text;
            console.log('dataConverter处理后:', cellContent);
            
            document.getElementById('result').innerHTML = `
                <h4>数据转换结果:</h4>
                <p><strong>原始文本:</strong> ${cell.text}</p>
                <p><strong>包含&lt;br&gt;:</strong> ${cell.text.includes('<br>')}</p>
                <p><strong>转换后:</strong> ${cellContent}</p>
            `;
        }

        function testRenderLogic() {
            console.log('=== 开始测试渲染逻辑 ===');
            
            const cellContent = "QB/T1002-2015<br>等级：合格品";
            console.log('单元格内容:', cellContent);
            
            // 模拟LabelRenderer的处理逻辑
            let displayContent = cellContent;
            const hasLineBreaks = displayContent.includes('<br>');
            console.log('检测到换行标签:', hasLineBreaks);
            
            if (hasLineBreaks) {
                displayContent = displayContent.replace(/<br\s*\/?>/gi, '\n');
                console.log('转换后的显示内容:', displayContent);
            }
            
            document.getElementById('result').innerHTML = `
                <h4>渲染逻辑测试:</h4>
                <p><strong>原始内容:</strong> ${cellContent}</p>
                <p><strong>检测到&lt;br&gt;:</strong> ${hasLineBreaks}</p>
                <p><strong>转换后内容:</strong></p>
                <div style="border: 1px solid #ccc; padding: 10px; white-space: pre-line; background: white;">
                    ${displayContent}
                </div>
                <p><strong>应该显示为两行文本</strong></p>
            `;
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testDataConversion();
        };
    </script>
</body>
</html>
