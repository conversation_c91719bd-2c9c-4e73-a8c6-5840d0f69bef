import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Space, Tag, Divider, Input, message, Tooltip } from 'antd';
import { QRCodeSVG } from 'qrcode.react';
import BarcodeRenderer from './BarcodeRenderer';
import EditableText from './EditableText';

const { Title, Text, Paragraph } = Typography;

// 元素类型映射
const ELEMENT_TYPE_NAMES = {
  '1': '文本',
  '2': '条形码',
  '3': '画布',
  '4': '线条',
  '5': 'Logo',
  '6': '图片',
  '7': '二维码',
  '8': '圆形',
  '9': '时间',
  '10': '表格',
  '11': '矩形'
};

const LabelRenderer = ({ data, onDataChange, textinData }) => {
  const [localData, setLocalData] = useState(data || []);
  const [containerWidth, setContainerWidth] = useState(800);
  const [showCharBounds, setShowCharBounds] = useState(true); // 控制是否显示字符边框
  const containerRef = useRef(null);

  useEffect(() => {
    setLocalData(data || []);
  }, [data]);

  // textinData 传递监听已移除

  // 渲染字符边框
  const renderCharBounds = () => {
    // 移除 renderCharBounds 调用的调试日志

    if (!showCharBounds) {
      return null;
    }

    if (!textinData || !textinData.result || !textinData.result.pages) {
      return null;
    }

    const charBounds = [];
    const pages = textinData.result.pages;

    // 移除页面数据处理的调试日志

    pages.forEach((page, pageIndex) => {
      if (page.content && Array.isArray(page.content)) {
        page.content.forEach((contentItem, contentIndex) => {

          if (contentItem.char_pos && Array.isArray(contentItem.char_pos)) {
            contentItem.char_pos.forEach((charPos, charIndex) => {
              if (Array.isArray(charPos) && charPos.length >= 8) {
                // 计算字符边界框
                const minX = Math.min(charPos[0], charPos[2], charPos[4], charPos[6]);
                const maxX = Math.max(charPos[0], charPos[2], charPos[4], charPos[6]);
                const minY = Math.min(charPos[1], charPos[3], charPos[5], charPos[7]);
                const maxY = Math.max(charPos[1], charPos[3], charPos[5], charPos[7]);

                // 获取正确的缩放比例
                // TextIn 返回的坐标是基于原始图片像素的，我们需要将其转换为当前渲染尺寸
                const canvasElement = localData.find(item => item.type === '3');
                const canvasWidthMm = canvasElement ? parseFloat(canvasElement.width) || 40 : 40;
                const canvasHeightMm = canvasElement ? parseFloat(canvasElement.height) || 30 : 30;

                // 计算渲染画布的像素尺寸
                const renderScale = containerWidth / canvasWidthMm; // px/mm
                const renderCanvasWidth = canvasWidthMm * renderScale;
                const renderCanvasHeight = canvasHeightMm * renderScale;

                // TextIn 图片的原始尺寸（从 metrics 中获取）
                const originalImageWidth = textinData.metrics?.[0]?.page_image_width || 480;
                const originalImageHeight = textinData.metrics?.[0]?.page_image_height || 360;

                // 计算从原始图片坐标到渲染坐标的缩放比例
                const scaleX = renderCanvasWidth / originalImageWidth;
                const scaleY = renderCanvasHeight / originalImageHeight;

                // 转换为渲染坐标
                const x = minX * scaleX;
                const y = minY * scaleY;
                const width = (maxX - minX) * scaleX;
                const height = (maxY - minY) * scaleY;

                // 移除字符坐标转换的调试日志

                charBounds.push(
                  <div
                    key={`char-${contentItem.id}-${charIndex}`}
                    style={{
                      position: 'absolute',
                      left: `${x}px`,
                      top: `${y}px`,
                      width: `${width}px`,
                      height: `${height}px`,
                      border: '2px solid red',
                      backgroundColor: 'rgba(255, 0, 0, 0.2)',
                      pointerEvents: 'none',
                      zIndex: 9999,
                      fontSize: '10px',
                      color: 'red',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 'bold',
                      boxSizing: 'border-box'
                    }}
                    title={`字符: ${contentItem.text[charIndex] || '?'} | 位置: [${charPos.join(',')}]`}
                  >
                    {contentItem.text[charIndex] || '?'}
                  </div>
                );

                // 移除字符边框生成的调试日志
              }
            });
          }
        });
      }
    });

    // 添加一个测试边框，确保渲染机制正常
    charBounds.push(
      <div
        key="test-border"
        style={{
          position: 'absolute',
          left: '10px',
          top: '10px',
          width: '100px',
          height: '50px',
          border: '3px solid blue',
          backgroundColor: 'rgba(0, 0, 255, 0.3)',
          zIndex: 9999,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'blue',
          fontWeight: 'bold'
        }}
      >
        测试边框
      </div>
    );

    // 移除字符边框完成的调试日志
    return charBounds;
  };

  // 监听条码解码完成事件
  useEffect(() => {
    const handleBarcodeDecoded = (event) => {
      const { base64, content, imageId, position } = event.detail;
      console.log('收到条码解码事件:', event.detail);

      // 使用 setTimeout 避免在渲染过程中更新状态
      setTimeout(() => {
        setLocalData(prevData => {
          const newData = prevData.map(element => {
            // 精确匹配条码元素
            if ((element.type === '2' || element.type === '7') &&
                element.content &&
                element.content.includes('识别中')) {

              // 通过多种方式匹配同一个条码
              let isMatch = false;

              // 方法1: 通过 imageId 匹配
              if (imageId && element.imageId === imageId) {
                isMatch = true;
              }

              // 方法2: 通过 base64 数据匹配
              if (!isMatch && base64 && element.base64Data === base64) {
                isMatch = true;
              }

              // 方法3: 通过位置匹配（比较位置数组）
              if (!isMatch && position && element.originalPosition) {
                const posMatch = JSON.stringify(position) === JSON.stringify(element.originalPosition);
                if (posMatch) {
                  isMatch = true;
                }
              }

              if (isMatch) {
                console.log('精确匹配并更新条码内容:', element.content, '->', content);
                return { ...element, content: content };
              }
            }
            return element;
          });

          // 通知父组件数据变化
          if (onDataChange) {
            onDataChange(newData);
          }

          return newData;
        });
      }, 0);
    };

    window.addEventListener('barcodeDecoded', handleBarcodeDecoded);

    return () => {
      window.removeEventListener('barcodeDecoded', handleBarcodeDecoded);
    };
  }, [onDataChange]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(entries => {
      if (entries[0]) {
        setContainerWidth(entries[0].contentRect.width);
      }
    });

    const currentRef = containerRef.current;
    if (currentRef) {
      resizeObserver.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        resizeObserver.unobserve(currentRef);
      }
    };
  }, []);

  // 更新元素内容
  const updateElementContent = (index, newContent) => {
    const newData = [...localData];
    newData[index] = { ...newData[index], content: newContent };
    setLocalData(newData);

    // 通知父组件数据变化
    if (onDataChange) {
      onDataChange(newData);
    }
  };

  if (!localData || !Array.isArray(localData) || localData.length === 0) {
    return (
      <Card title="标签预览">
        <Text type="secondary">暂无数据</Text>
      </Card>
    );
  }

  // 查找画布元素
  const canvasElement = localData.find(item => item.type === '3' || item.elementType === '3');
  const canvasWidthMm = canvasElement ? parseFloat(canvasElement.width) || 40 : 40;
  const canvasHeightMm = canvasElement ? parseFloat(canvasElement.height) || 30 : 30;

  // 计算缩放比例，将mm单位的画布渲染到合适的像素尺寸
  const maxDisplayWidthPx = containerWidth > 0 ? containerWidth : 800; // 使用动态宽度
  const scale = maxDisplayWidthPx / canvasWidthMm; // px/mm

  const displayWidth = canvasWidthMm * scale;
  const displayHeight = canvasHeightMm * scale;

  // 过滤掉画布元素，只渲染其他元素
  const renderElements = localData.filter(item => item.type !== '3' && item.elementType !== '3');

  const renderElement = (element, renderIndex) => {
    // 找到元素在原始数据中的真实索引
    const actualIndex = localData.findIndex(item => item === element);
    const x = (parseFloat(element.x) || 0) * scale;
    const y = (parseFloat(element.y) || 0) * scale;
    const width = (parseFloat(element.width) || 10) * scale;
    const height = (parseFloat(element.height) || 5) * scale;
    const rotation = parseInt(element.rotational) || 0;

    // 计算字体大小：优先使用 textSize 属性（像素单位），备选动态计算
    const textSizePx = parseFloat(element.textSize);
    const calculatedFontSize = Math.max(10, height * 0.6);
    const fontSize = (textSizePx && textSizePx > 0) ? textSizePx : calculatedFontSize;

    // 获取字符间距
    const wordSpaceMm = parseFloat(element.wordSpace) || 0;
    const wordSpacePx = wordSpaceMm * scale;

    const baseStyle = {
      position: 'absolute',
      left: `${x}px`,
      top: `${y}px`,
      width: `${width}px`,
      height: `${height}px`,
      transform: rotation !== 0 ? `rotate(${rotation}deg)` : 'none',
      transformOrigin: 'top left',
      border: '1px dashed #ccc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: wordSpacePx > 0 ? 'flex-start' : 'center', // 有字符间距时左对齐，否则居中
      fontSize: `${fontSize}px`,
      overflow: 'hidden'
    };

    switch (element.type) {
      case '1': // 文本
        // 如果有字符间距，使用自定义渲染
        if (wordSpacePx > 0) {
          return (
            <div
              key={renderIndex}
              style={{
                ...baseStyle,
                backgroundColor: 'rgba(24, 144, 255, 0.1)',
                borderColor: '#1890ff',
                padding: '2px',
                fontWeight: element.bold === 'true' ? 'bold' : 'normal',
                fontStyle: element.italic === 'true' ? 'italic' : 'normal',
                textDecoration: element.underline === 'true' ? 'underline' : 'none'
              }}
              title={`文本: ${element.content || '空文本'} (点击编辑) | 字符间距: ${wordSpaceMm}mm`}
              onClick={() => {
                const newContent = prompt('编辑文本内容:', element.content || '');
                if (newContent !== null && newContent !== element.content) {
                  updateElementContent(actualIndex, newContent);
                }
              }}
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                height: '100%',
                fontSize: `${fontSize}px`,
                justifyContent: element.hAlignment === '2' ? 'center' :
                  element.hAlignment === '3' ? 'flex-end' : 'flex-start'
              }}>
                {(element.content || '').split('').map((char, charIndex) => (
                  <span
                    key={charIndex}
                    style={{
                      marginRight: charIndex < (element.content || '').length - 1 ? `${wordSpacePx}px` : '0'
                    }}
                  >
                    {char}
                  </span>
                ))}
              </div>
            </div>
          );
        }

        // 没有字符间距时使用原有的 EditableText 组件
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(24, 144, 255, 0.1)',
              borderColor: '#1890ff',
              padding: '2px',
              textAlign: element.hAlignment === '2' ? 'center' :
                element.hAlignment === '3' ? 'right' : 'left',
              fontWeight: element.bold === 'true' ? 'bold' : 'normal',
              fontStyle: element.italic === 'true' ? 'italic' : 'normal',
              textDecoration: element.underline === 'true' ? 'underline' : 'none'
            }}
            title={`文本: ${element.content || '空文本'} (点击编辑)`}
          >
            <EditableText
              value={element.content || ''}
              onChange={(newContent) => updateElementContent(actualIndex, newContent)}
              placeholder="点击编辑文本"
              stretch={true}
              style={{
                fontSize: 'inherit',
                fontWeight: 'inherit',
                fontStyle: 'inherit',
                textDecoration: 'inherit',
                textAlign: 'inherit'
              }}
              textStyle={{
                fontSize: 'inherit',
                lineHeight: '1.2',
                wordBreak: 'break-all'
              }}
            />
          </div>
        );

      case '2': // 条形码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(82, 196, 26, 0.1)',
              borderColor: '#52c41a',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title={`条形码: ${element.content || '123456789'} (点击编辑)`}
          >
            <div style={{ width: '100%', height: '100%', position: 'relative' }}>
              <BarcodeRenderer
                value={element.content || '123456789'}
                format={element.barcodeType || 'CODE128'}
                width={Math.max(1, Math.floor(width / 100))}
                height={Math.max(20, height - 20)}
                displayValue={element.showText !== 'false'}
                fontSize={Math.max(8, Math.floor(height / 8))}
                textAlign={element.textAlignment === '1' ? 'left' :
                  element.textAlignment === '3' ? 'right' : 'center'}
                style={{
                  width: '100%',
                  height: '100%'
                }}
              />
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  cursor: 'pointer',
                  background: 'transparent'
                }}
                onClick={() => {
                  const newContent = prompt('请输入条码内容:', element.content || '123456789');
                  if (newContent !== null && newContent !== element.content) {
                    updateElementContent(index, newContent);
                  }
                }}
              />
            </div>
          </div>
        );

      case '7': // 二维码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(250, 173, 20, 0.1)',
              borderColor: '#faad14',
              padding: '4px',
              position: 'relative'
            }}
            title={`二维码: ${element.content || 'QR Code'} (点击编辑)`}
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {width > 30 && height > 30 ? (
                <QRCodeSVG
                  value={element.content || 'QR Code'}
                  size={Math.min(width - 8, height - 8)}
                  level="M"
                />
              ) : (
                <div style={{
                  width: '80%',
                  height: '80%',
                  backgroundColor: '#000',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '6px'
                }}>
                  QR
                </div>
              )}
            </div>
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                cursor: 'pointer',
                background: 'transparent'
              }}
              onClick={() => {
                const newContent = prompt('请输入二维码内容:', element.content || 'QR Code');
                if (newContent !== null && newContent !== element.content) {
                  updateElementContent(index, newContent);
                }
              }}
            />
          </div>
        );

      case '6': // 图片
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(114, 46, 209, 0.1)',
              borderColor: '#722ed1'
            }}
            title="图片"
          >
            {element.content && element.content.startsWith('data:image') ? (
              <img
                src={element.content}
                alt="识别的图片"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            ) : (
              <Text style={{ fontSize: 'inherit' }}>图片</Text>
            )}
          </div>
        );

      case '10': // 表格
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(235, 47, 6, 0.1)',
              borderColor: '#eb2f06',
              padding: '2px'
            }}
            title="表格"
          >
            {element.cells && Array.isArray(element.cells) ? (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'grid',
                gridTemplateColumns: element.columnWidths && element.columnWidths.length > 0
                  ? element.columnWidths.map(w => `${w}fr`).join(' ')
                  : 'repeat(auto-fit, minmax(20px, 1fr))',
                gridTemplateRows: element.rowHeights && element.rowHeights.length > 0
                  ? element.rowHeights.map(h => `${h}fr`).join(' ')
                  : 'repeat(auto-fit, minmax(10px, 1fr))',
                gap: '1px',
                backgroundColor: '#ccc'
              }}>
                {element.cells.map((cell, cellIndex) => {
                  // 解析单元格内容的 markdown 样式
                  const cellContent = cell.content || '';
                  const isBold = cellContent.startsWith('**') && cellContent.endsWith('**');
                  let displayContent = isBold ? cellContent.slice(2, -2) : cellContent;

                  // 处理 <br> 标签，将其转换为换行符
                  const hasLineBreaks = displayContent.includes('<br>');
                  if (hasLineBreaks) {
                    displayContent = displayContent.replace(/<br\s*\/?>/gi, '\n');
                  }

                  // 计算表格单元格字体大小：优先使用 textSize 属性（像素单位）
                  let fontSize;
                  const textSizePx = parseFloat(cell.textSize);
                  if (textSizePx && textSizePx > 0) {
                    // 直接使用指定的字体大小（已经是像素单位）
                    fontSize = textSizePx;
                  } else {
                    // 备选：动态计算字体大小
                    const cellRowIndex = parseInt(cell.row);
                    const rowSpan = parseInt(cell.rowSpan) || 1;

                    // 计算单元格在网格中的实际高度
                    let cellHeightPx = 0;
                    if (element.rowHeights && element.rowHeights.length > 0) {
                      // 累加跨越的行高
                      for (let i = cellRowIndex; i < cellRowIndex + rowSpan && i < element.rowHeights.length; i++) {
                        cellHeightPx += element.rowHeights[i] || 0;
                      }
                      // 转换为渲染像素
                      cellHeightPx = cellHeightPx * scale;
                    } else {
                      // 备选：使用表格总高度平均分配
                      const tableHeightPx = (parseFloat(element.height) || 100) * scale;
                      const estimatedRows = Math.max(1, element.cells ? Math.max(...element.cells.map(c => parseInt(c.row) + 1)) : 1);
                      cellHeightPx = (tableHeightPx / estimatedRows) * rowSpan;
                    }

                    // 考虑多行文本，调整字体大小
                    const lineCount = hasLineBreaks ? displayContent.split('\n').length : 1;
                    const adjustedCellHeight = cellHeightPx / lineCount;

                    // 使用与普通文本相同的计算方式：高度的60%
                    fontSize = Math.max(10, Math.min(24, adjustedCellHeight * 0.6));
                  }

                  return (
                    <div
                      key={cellIndex}
                      style={{
                        backgroundColor: 'white',
                        fontSize: fontSize + 'px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center', // 默认居中对齐
                        padding: '2px 4px',
                        gridColumn: `${parseInt(cell.col) + 1} / span ${parseInt(cell.colSpan) || 1}`,
                        gridRow: `${parseInt(cell.row) + 1} / span ${parseInt(cell.rowSpan) || 1}`,
                        fontWeight: (cell.bold === 'true' || isBold) ? 'bold' : 'normal',
                        fontStyle: cell.italic === 'true' ? 'italic' : 'normal',
                        textDecoration: cell.underline === 'true' ? 'underline' : 'none',
                        overflow: 'hidden',
                        cursor: 'pointer',
                        border: '1px solid #e8e8e8',
                        position: 'relative',
                        minHeight: '20px'
                      }}
                      title={`单元格 (${cell.row}, ${cell.col}): ${displayContent.replace(/\n/g, ' ') || '空'} (点击编辑)`}
                      onClick={() => {
                        const newContent = prompt(
                          `编辑单元格 (${parseInt(cell.row) + 1}, ${parseInt(cell.col) + 1}) 内容:`,
                          displayContent
                        );
                        if (newContent !== null && newContent !== displayContent) {
                          // 保持原有的 markdown 格式，并将换行符转换回 <br> 标签
                          let finalContent = newContent.replace(/\n/g, '<br>');
                          finalContent = isBold ? `**${finalContent}**` : finalContent;

                          // 更新单元格内容
                          const newData = [...localData];
                          const tableIndex = localData.findIndex(item => item === element);
                          if (tableIndex !== -1) {
                            const newCells = [...newData[tableIndex].cells];
                            newCells[cellIndex] = { ...newCells[cellIndex], content: finalContent };
                            newData[tableIndex] = { ...newData[tableIndex], cells: newCells };
                            setLocalData(newData);

                            // 通知父组件数据变化
                            if (onDataChange) {
                              onDataChange(newData);
                            }
                          }
                        }
                      }}
                    >
                      {hasLineBreaks ? (
                        // 如果有换行符，使用 pre-line 样式显示多行文本
                        <div
                          style={{
                            fontSize: fontSize + 'px',
                            fontWeight: (cell.bold === 'true' || isBold) ? 'bold' : 'normal',
                            fontStyle: cell.italic === 'true' ? 'italic' : 'normal',
                            textDecoration: cell.underline === 'true' ? 'underline' : 'none',
                            textAlign: 'center',
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            whiteSpace: 'pre-line',
                            lineHeight: '1.2',
                            wordBreak: 'break-word',
                            cursor: 'pointer',
                            padding: '2px 4px'
                          }}
                        >
                          {displayContent}
                        </div>
                      ) : (
                        // 单行文本使用 EditableText 组件
                        <EditableText
                          value={displayContent}
                          onChange={(newContent) => {
                            // 保持原有的 markdown 格式，并将换行符转换回 <br> 标签
                            let finalContent = newContent.replace(/\n/g, '<br>');
                            finalContent = isBold ? `**${finalContent}**` : finalContent;

                            // 更新单元格内容
                            const newData = [...localData];
                            const tableIndex = localData.findIndex(item => item === element);
                            if (tableIndex !== -1) {
                              const newCells = [...newData[tableIndex].cells];
                              newCells[cellIndex] = { ...newCells[cellIndex], content: finalContent };
                              newData[tableIndex] = { ...newData[tableIndex], cells: newCells };
                              setLocalData(newData);

                              // 通知父组件数据变化
                              if (onDataChange) {
                                onDataChange(newData);
                              }
                            }
                          }}
                          placeholder="点击编辑"
                          stretch={false}
                          style={{
                            fontSize: 'inherit',
                            fontWeight: 'inherit',
                            fontStyle: 'inherit',
                            textDecoration: 'inherit',
                            textAlign: 'center', // 默认居中对齐
                            width: '100%',
                            minHeight: '16px',
                            lineHeight: '1.2',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          textStyle={{
                            fontSize: 'inherit',
                            lineHeight: '1.2',
                            textAlign: 'center',
                            wordBreak: cell.lineWrap === 'true' ? 'break-word' : 'keep-all',
                            whiteSpace: cell.lineWrap === 'true' ? 'normal' : 'nowrap'
                          }}
                        />
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div style={{
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '8px',
                color: '#999'
              }}>
                表格数据缺失
              </div>
            )}
          </div>
        );

      default:
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(140, 140, 140, 0.1)',
              borderColor: '#8c8c8c'
            }}
            title={`${ELEMENT_TYPE_NAMES[element.type] || '未知类型'}`}
          >
            <Text style={{ fontSize: 'inherit' }}>
              {ELEMENT_TYPE_NAMES[element.type] || '未知'}
            </Text>
          </div>
        );
    }
  };

  return (
    <Card
      title="标签预览"
      extra={
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div>
            <Text strong>画布尺寸: </Text>
            <Text code>{canvasWidthMm} mm × {canvasHeightMm} mm</Text>
            <Text type="secondary" style={{ marginLeft: 16 }}>
              显示比例: 1mm ≈ {scale.toFixed(2)}px
            </Text>
          </div>
          <div>
            <label style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <input
                type="checkbox"
                checked={showCharBounds}
                onChange={(e) => setShowCharBounds(e.target.checked)}
              />
              <Text>显示字符边框</Text>
            </label>
          </div>
        </div>
      }
    >
      <div ref={containerRef}>
        {renderElements.length > 0 ? (
          <div
            style={{
              position: 'relative',
              width: `${displayWidth}px`,
              height: `${displayHeight}px`,
              backgroundColor: '#f9f9f9',
              border: '1px solid #e8e8e8',
              boxSizing: 'content-box',
              overflow: 'hidden'
            }}
          >
            {renderElements.map(renderElement)}
            {renderCharBounds()}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '60px 20px',
            color: '#999'
          }}>
            <Paragraph>
              暂无预览元素
            </Paragraph>
          </div>
        )}
      </div>
      <Divider />
      <Title level={5}>元素数据</Title>
      <div style={{ maxHeight: 400, overflowY: 'auto', padding: '0 8px' }}>
        {localData.map((element, index) => (
          <Card key={index} size="small" style={{ marginBottom: 12 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Tag color="blue">
                  {ELEMENT_TYPE_NAMES[element.elementType || element.type] || '未知'}
                </Tag>
                <Text>
                  位置: ({element.x} mm, {element.y} mm)
                </Text>
                <Text style={{ marginLeft: 8 }}>
                  尺寸: {element.width} mm × {element.height} mm
                </Text>
                {element.content && (
                  <Tooltip title={element.content}>
                    <Paragraph
                      ellipsis={{ rows: 1 }}
                      style={{ maxWidth: 300, margin: '8px 0 0' }}
                    >
                      内容: <Text type="secondary">{element.content}</Text>
                    </Paragraph>
                  </Tooltip>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </Card>
  );
};

export default LabelRenderer;
