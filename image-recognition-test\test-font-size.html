<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体大小测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-table {
            border-collapse: collapse;
            margin: 10px 0;
        }
        .test-cell {
            border: 1px solid #ccc;
            text-align: center;
            vertical-align: middle;
            background: white;
        }
        .size-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .comparison {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        .comparison > div {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>表格字体大小优化测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 字体大小计算逻辑</div>
            <p><strong>改进前</strong>：使用单元格高度的40%，范围限制在10-24px</p>
            <p><strong>改进后</strong>：</p>
            <ul>
                <li>考虑文本行数（<code>&lt;br&gt;</code>标签）</li>
                <li>估算每行字符数（中文按2倍宽度计算）</li>
                <li>同时考虑高度和宽度限制</li>
                <li>转换为毫米单位，再转换为渲染像素</li>
                <li>范围：1-8mm（约3.78-30.24px）</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">2. 不同单元格尺寸的字体效果对比</div>
            <div class="comparison">
                <div>
                    <h4>小单元格 (100×50px)</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 100px; height: 50px; font-size: 11.34px;">
                                品名
                                <div class="size-info">11.34px (3mm)</div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div>
                    <h4>中等单元格 (200×80px)</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 200px; height: 80px; font-size: 18.9px;">
                                女鞋
                                <div class="size-info">18.9px (5mm)</div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div>
                    <h4>大单元格 (300×120px)</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 300px; height: 120px; font-size: 26.46px;">
                                执行标准
                                <div class="size-info">26.46px (7mm)</div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 多行文本字体大小测试</div>
            <div class="comparison">
                <div>
                    <h4>单行文本</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 200px; height: 80px; font-size: 18.9px;">
                                QB/T1002-2015
                                <div class="size-info">18.9px (5mm)</div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div>
                    <h4>双行文本</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 200px; height: 80px; font-size: 14.55px; white-space: pre-line; line-height: 1.3;">QB/T1002-2015
等级：合格品
                                <div class="size-info">14.55px (3.85mm)</div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 字符宽度考虑</div>
            <div class="comparison">
                <div>
                    <h4>纯中文</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 150px; height: 60px; font-size: 18.9px;">
                                执行标准
                                <div class="size-info">4个中文字符</div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div>
                    <h4>中英混合</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 150px; height: 60px; font-size: 15.12px;">
                                QB/T1002-2015
                                <div class="size-info">13个字符（含符号）</div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div>
                    <h4>纯数字</h4>
                    <table class="test-table">
                        <tr>
                            <td class="test-cell" style="width: 150px; height: 60px; font-size: 22.68px;">
                                023-552
                                <div class="size-info">7个字符</div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 测试说明</div>
            <p><strong>字体大小计算公式</strong>：</p>
            <pre style="background: #f8f8f8; padding: 10px; border-radius: 4px;">
// 基于高度计算
fontHeightFromCellHeight = cellHeight / (lineCount * 1.3)

// 基于宽度计算  
fontSizeFromCellWidth = cellWidth / (avgCharsPerLine * 0.8)

// 取较小值
calculatedFontSize = min(fontHeightFromCellHeight, fontSizeFromCellWidth)

// 转换为毫米
mmFontSize = calculatedFontSize * scaleY

// 限制范围并转换为像素
finalSize = max(1, min(8, mmFontSize)) * 3.78
            </pre>
            <p><strong>优势</strong>：</p>
            <ul>
                <li>✅ 考虑文本实际内容和行数</li>
                <li>✅ 同时限制高度和宽度，避免溢出</li>
                <li>✅ 区分中英文字符宽度</li>
                <li>✅ 使用真实的坐标转换比例</li>
                <li>✅ 合理的字体大小范围</li>
            </ul>
        </div>
    </div>
</body>
</html>
